# 鑫晴数智H200 DeepSeek-R1部署完整指南

## 1. 环境准备与访问

### 1.1 访问凭据

* **VPN连接地址:** `***************:65443`
* **堡垒机访问地址:** `http://************/`
* **用户名:** `xqsz5`
* **密码:** `Ac2^G56we@`
* **注:** VPN与堡垒机共用相同凭据

### 1.2 硬件要求

* NVIDIA H200 GPU (8卡配置)
* 充足的系统内存(推荐128GB以上)
* 高速网络连接(推荐25Gbps以上)
* 足够的存储空间(至少500GB可用空间)

## 2. 网络接入步骤

### 2.1 安装VPN客户端

* **Windows:** 安装 `iNodeSetup7.3 (E0630).exe`
* **Mac:** 解压并安装 `iNodeClient.zip`(普通版本)或 `iNodeClient-macos15.zip`(macOS 15适配版)
* 按照安装向导完成设置

### 2.2 连接VPN

* 启动已安装的VPN客户端
* 输入连接地址: `***************:65443`
* 输入用户名: `xqsz5`和密码: `Ac2^G56we@`
* 确认连接状态显示为"已连接"

## 3. 堡垒机与服务器访问

### 3.1 登录堡垒机

* 在浏览器中访问: `http://************/`
* 输入用户名: `xqsz5`和密码: `Ac2^G56we@`
* 登录JumpServer堡垒机平台

### 3.2 连接GPU服务器

* 在JumpServer界面导航至"资产列表"
* 找到分配给您的H200服务器(如 `*************`)
* 点击"连接"按钮进入Web终端
* 使用分配的凭据登录服务器

### 3.3 验证GPU环境

```bash
# 检查GPU状态和驱动版本
nvidia-smi

# 查看CUDA版本
nvcc --version

# 检查可用内存
free -h
```

## 4. 模型部署准备

### 4.1 创建工作目录

```bash
# 创建模型存储目录
mkdir -p /export/DeepSeek-R1

# 创建HuggingFace缓存目录
mkdir -p /export/hf_cache

# 检查磁盘空间
df -h /export
```

### 4.2 安装必要工具

```bash
# 更新软件包并安装rclone(高效文件复制工具)
apt update && apt install -y rclone curl jq

# 如果需要下载模型文件，安装Hugging Face CLI
pip install -U huggingface_hub
```

## 5. 模型获取

### 5.1 从Hugging Face下载(推荐方式)

```bash
# 设置环境变量使用国内镜像加速
export HF_ENDPOINT="https://hf-mirror.com"

# 下载DeepSeek-R1模型(带强制重新下载选项)
huggingface-cli download deepseek-ai/DeepSeek-R1 \
  --local-dir /export/DeepSeek-R1 \
  --force-download
```

### 5.2 下载问题解决方案

如遇到 `Consistency check failed`错误：

```bash
# 总是使用--force-download参数
huggingface-cli download deepseek-ai/DeepSeek-R1 \
  --local-dir /export/DeepSeek-R1 \
  --force-download

# 如果仍有问题，可以尝试逐个下载模型文件
huggingface-cli download deepseek-ai/DeepSeek-R1 \
  --include="model-00007-of-000163.safetensors" \
  --local-dir /export/DeepSeek-R1 \
  --force-download

# 清理损坏的缓存文件后再尝试
find /export/hf_cache -name "*.incomplete" -delete
```

## 6. DeepSeek-R1推理服务部署

### 6.1 获取Docker镜像

```bash
# 拉取适配H200的sglang镜像
docker pull registry.hd-01.alayanew.com:8443/vc-zhaohx/lmsysorg/sglang:v0.4.3.post4-cu125

# 检查镜像是否成功拉取
docker images | grep sglang
```

### 6.2 启动推理服务容器

```bash
docker run -d --rm \
  --gpus all \
  --ipc=host \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  -e NCCL_IB_DISABLE="0" \
  -e NCCL_SOCKET_IFNAME="bond0" \
  -e "NCCL_IB_HCA=mlx5_gdr_0,mlx5_gdr_1,mlx5_gdr_2,mlx5_gdr_3,mlx5_gdr_4,mlx5_gdr_5,mlx5_gdr_6,mlx5_gdr_7,mlx5_1,mlx5_2" \
  -e NCCL_DEBUG="INFO" \
  -e HF_HUB_CACHE="/hf_cache_in_container" \
  -e HF_ENDPOINT="https://hf-mirror.com" \
  -p 40000:40000 \
  -p 21000:21000 \
  -v /export/DeepSeek-R1:/model_files_on_host:ro \
  -v /export/hf_cache:/hf_cache_in_container \
  --shm-size=1000g \
  --name sglang_deepseek_r1 \
  registry.hd-01.alayanew.com:8443/vc-zhaohx/lmsysorg/sglang:v0.4.3.post4-cu125 \
  /bin/bash -c " \
    set -e; \
    apt update && apt install -y rclone && \
    echo '创建内存目录 /dev/shm/DeepSeek-R1...' && \
    mkdir -p /dev/shm/DeepSeek-R1 && \
    echo '拷贝模型文件到内存中以优化推理速度...' && \
    rclone copy /model_files_on_host /dev/shm/DeepSeek-R1 --transfers 96 --progress && \
    echo '模型复制完成，启动SGLang服务...' && \
    python3 -m sglang.launch_server \
      --model-path /dev/shm/DeepSeek-R1 \
      --tensor-parallel-size 8 \
      --host 0.0.0.0 \
      --port 40000 \
      --log-level INFO \
      --mem-fraction-static 0.9 \
      --dist-init-method tcp://localhost:21000 \
      --nnodes 1 \
      --node-rank 0 \
      --tp-master-port 21001 \
      --trust-remote-code \
      --enable-metrics \
      --served-model-name deepseek-r1 \
      --reasoning-parser deepseek-r1 \
      --enable-flashinfer-mla \
      --enable-mixed-chunk \
      --num-continuous-decode-steps 10 \
      --watchdog-timeout 7200 \
  "
```

### 6.3 参数解析

* `--gpus all`: 使用所有可用GPU
* `--tensor-parallel-size 8`: 配置为8卡张量并行
* `--mem-fraction-static 0.9`: 分配90%的GPU显存给服务
* `--watchdog-timeout 7200`: 设置看门狗超时时间为2小时
* `--enable-flashinfer-mla`: 启用FlashInfer优化加速推理
* `--shm-size=1000g`: 为容器分配1TB共享内存用于模型加载

## 7. 服务验证与使用

### 7.1 检查服务状态

```bash
# 查看容器运行状态
docker ps | grep sglang_deepseek_r1

# 实时查看服务日志
docker logs -f sglang_deepseek_r1

# 查看GPU使用情况
nvidia-smi -l 1
```

### 7.2 API测试

```bash
# 简单对话测试
curl -X POST http://localhost:40000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
      "model": "deepseek-r1",
      "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "你好，请介绍一下自己。"}
      ]
  }'
  
curl -X POST http://***************:40000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
      "model": "/deepseek-r1",
      "messages": [
          {"role": "user", "content": "解释下什么是中国民族伟大复兴。"}
      ]
  }'

curl -X POST http://*************:40000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
      "model": "DeepSeek-R1",
      "messages": [
          {"role": "user", "content": "你好，请介绍一下自己。"}
      ],
      "max_tokens": 512
  }'

# 更复杂的系统指令测试
curl -X POST http://localhost:40000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "/model",
    "messages": [
      {"role": "system", "content": "你是一个专业的AI助手，擅长解答科技问题。"},
      {"role": "user", "content": "解释一下大语言模型的工作原理。"}
    ],
    "temperature": 0.3,
    "max_tokens": 1024
  }' | jq
```

### 7.3 性能测试

```bash
# 测试服务的吞吐量(每秒token数)
python3 -c '
import requests
import json
import time

url = "http://localhost:40000/v1/chat/completions"
headers = {"Content-Type": "application/json"}
payload = {
    "model": "deepseek-r1",
    "messages": [{"role": "user", "content": "写一篇500字的关于人工智能发展的文章"}],
    "temperature": 0.7,
    "max_tokens": 1024
}

start_time = time.time()
response = requests.post(url, headers=headers, data=json.dumps(payload))
result = response.json()
end_time = time.time()

if "choices" in result and len(result["choices"]) > 0:
    output = result["choices"][0]["message"]["content"]
    tokens = result.get("usage", {}).get("completion_tokens", 0)
    duration = end_time - start_time
    tokens_per_second = tokens / duration
    print(f"生成了 {tokens} tokens，耗时 {duration:.2f} 秒")
    print(f"吞吐量: {tokens_per_second:.2f} tokens/sec")
    print(f"输出结果:\n{output[:100]}...")
else:
    print(f"错误: {result}")
'
```

## 8. 高级配置与优化

### 8.1 多机多卡部署

针对更大规模部署，可以配置多节点:

```bash
# 在每个节点上按顺序启动，修改--node-rank参数
# 节点1(主节点)
--nnodes 2 --node-rank 0 --dist-init-method tcp://[主节点IP]:21000

# 节点2
--nnodes 2 --node-rank 1 --dist-init-method tcp://[主节点IP]:21000
```

### 8.2 性能调优

```bash
# 内存优化参数
--mem-fraction-dynamic 0.85  # 动态内存分配比例
--disable-custom-all-reduce  # 如果遇到稳定性问题，尝试禁用自定义全归约
```

### 8.3 服务管理命令

```bash
# 停止服务
docker stop sglang_deepseek_r1

# 查看服务资源使用
docker stats sglang_deepseek_r1

# 重启服务(如有异常)
docker restart sglang_deepseek_r1
```

## 9. 常见问题与解决方案

### 9.1 下载失败

* 使用 `--force-download`参数，并确保网络稳定
* 尝试更换为其他镜像源，例如:`https://hf-mirror.com`或 `https://huggingface.co`
* 如果特定文件持续下载失败，可尝试使用备用下载工具如 `aria2c`或 `wget`

### 9.2 GPU问题

* 确保安装了正确版本的驱动(推荐560.xx以上)
* 确保CUDA版本兼容(推荐12.x)
* 检查GPU连接状态和功率供应

### 9.3 服务启动失败

* 检查Docker日志获取详细错误信息
* 确保模型文件完整且可访问
* 验证共享内存配置是否足够
* 检查端口是否被占用

### 9.4 推理性能问题

* 增加 `--num-continuous-decode-steps`值可提高吞吐量
* 调整 `--mem-fraction-static`和 `--mem-fraction-dynamic`参数
* 确认是否正确启用了硬件优化参数

## 10. 安全与维护

### 10.1 安全建议

* 修改默认密码
* 限制API访问范围(设置防火墙规则)
* 定期更新系统和Docker镜像

### 10.2 定期维护

* 监控磁盘空间和GPU使用率
* 定期清理缓存文件
* 定期备份关键配置

---

**注意:** 本部署方案针对H200 GPU优化，可根据实际硬件环境调整相关参数。成功部署后，服务将在端口40000提供API接口，可使用类似OpenAI API的方式进行调用。

pkill -9 -f sglang  # 强制杀死所有sglang相关进程
pkill -9 -f python  # 可能需要杀死所有python进程
docker rm -f $(docker ps -aq) # 移除所有容器

docker run -d --gpus all --ipc=host --network host 
  -v /mnt/nvme2/DeepSeek-R1-0605:/model:ro 
  --name vllm_deepseek 
  vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 8 
  --max-model-len 32768 
  --dtype bfloat16 
  --port 40000

# 查看日志

docker logs vllm_deepseek

# 实时查看日志(跟踪新的日志输出)

docker logs -f vllm_deepseek

# 查看最近100行日志

docker logs --tail 100 vllm_deepseek

# 查看特定时间段的日志

docker logs --since 2023-06-04T00:00:00 vllm_deepseek

# 查找使用GPU的进程

nvidia-smi

# 终止所有Docker容器

docker rm -f $(docker ps -aq)

# 强制终止所有Python进程

pkill -9 -f python

docker rm -f vllm_deepseek

docker run -d --gpus all --ipc=host --network host 
  --ulimit memlock=-1 --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  --shm-size=950g 
  -v /mnt/nvme2/DeepSeek-R1-0605:/model_files:ro 
  --name vllm_deepseek 
  --entrypoint /bin/bash 
  vllm/vllm-openai:latest 
  -c "set -e; 
    apt update && apt install -y rclone &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    echo '复制模型到共享内存...' &&
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress &&
    python3 -m vllm.entrypoints.openai.api_server
    --model /dev/shm/DeepSeek-R1
    --served-model-name deepseek-reasoner
    --tensor-parallel-size 8
    --max-model-len 32768
    --dtype bfloat16
    --port 40000
    --host 0.0.0.0
    --trust-remote-code
    --reasoning-parser deepseek_r1
    --enable-reasoning
    --gpu-memory-utilization 0.95
    --max-num-batched-tokens 16384
    --enable-chunked-prefill
    --scheduler-delay-factor 0.02
    --enable-prefix-caching"

// 成功

# 停止当前容器

docker rm -f vllm_deepseek

docker run -d --gpus all --ipc=host --network host 
  -v /mnt/nvme2/DeepSeek-R1-0605:/deepseek-reasoner:ro 
  --name vllm_deepseek 
  vllm/vllm-openai:latest 
  --model /deepseek-reasoner 
  --served-model-name deepseek-reasoner 
  --tensor-parallel-size 8 
  --max-model-len 32768 
  --dtype bfloat16 
  --port 40000 
  --trust-remote-code 
  --reasoning-parser deepseek_r1 
  --enable-reasoning 
  --gpu-memory-utilization 0.9

python3 -m sglang.launch_server 
  --model-path /export/DeepSeek-R1 
  --tp 8 
  --dtype auto 
  --trust-remote-code 
  --disable-cuda-graph 
  --mem-fraction-static 0.7 
  --served-model-name deepseek-r1 
  --host 0.0.0.0 
  --port 40000 
  --attention-backend flashinfer 
  --enable-mixed-chunk 
  --num-continuous-decode-steps 10

huggingface-cli download --resume-download deepseek-ai/DeepSeek-R1-0528 --local-dir /mnt/nvme2/DeepSeek-R1-0528/
huggingface-cli download --resume-download deepseek-ai/DeepSeek-R1 --local-dir /export/DeepSeek-R1/

# 终止所有Docker容器

docker rm -f $(docker ps -aq)

# 强制终止所有Python进程

pkill -9 -f python

# 使用本地sglang镜像运行服务

docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  --shm-size=950g 
  -v /mnt/nvme2/DeepSeek-R1-0605:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:latest 
  /bin/bash -c " 
    set -e;
    echo '--- 正在清空并准备共享内存目录 ---' &&
    rm -rf /dev/shm/DeepSeek-R1/* &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    echo '--- 正在复制模型到共享内存 (/dev/shm/DeepSeek-R1) 以获得最大速度 ---' &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress &&
    echo '--- 模型复制完成。启动SGLang服务 ---' &&
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --dtype auto
    --trust-remote-code
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 16
    --kv-cache-dtype auto
  "

# 不清空版

docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  --shm-size=950g 
  -v /mnt/nvme2/DeepSeek-R1-0605:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:latest 
  /bin/bash -c " 
    set -e;
    echo '--- 检查共享内存中是否已存在模型 ---' &&
    if [ -f /dev/shm/DeepSeek-R1/config.json ] && [ -f /dev/shm/DeepSeek-R1/model.safetensors.index.json ]; then
    echo '--- 模型已存在，跳过复制步骤 ---';
    else
    echo '--- 准备共享内存目录 ---' &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    echo '--- 正在复制模型到共享内存 (/dev/shm/DeepSeek-R1) 以获得最大速度 ---' &&
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress;
    fi &&
    echo '--- 启动SGLang服务 ---' &&
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --dtype auto
    --trust-remote-code
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 16
    --kv-cache-dtype auto
  "
docker logs -f sglang_deepseek_r1

docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v /mnt/nvme2/DeepSeek-R1-0605:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:latest 
  /bin/bash -c " 
    set -e;
    echo '--- 检查共享内存中是否已存在模型 ---' &&
    if [ -f /dev/shm/DeepSeek-R1/config.json ] && [ -f /dev/shm/DeepSeek-R1/model.safetensors.index.json ]; then
    echo '--- 模型已存在，跳过复制步骤 ---';
    else
    echo '--- 准备共享内存目录 ---' &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    echo '--- 正在复制模型到共享内存 (/dev/shm/DeepSeek-R1) 以获得最大速度 ---' &&
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress;
    fi &&
    python3 -m sglang.compile_deep_gemm --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code &&
    echo '--- 启动SGLang服务 ---' &&
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --dtype auto
    --trust-remote-code
    --mem-fraction-static 0.9
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 20
    --kv-cache-dtype fp8_e5m2
    --api-key sk-deepseek-123456,sk-deepseek-admin789
    --max-running-requests 600
    --watchdog-timeout 7200
    --reasoning-parser deepseek-r1
  "

# 0528版

docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  --shm-size=950g 
  -v /mnt/nvme2/DeepSeek-R1-0528:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1_0528 
  lmsysorg/sglang:latest 
  /bin/bash -c " 
    set -e;
    echo '--- 正在清空并准备共享内存目录 ---' &&
    rm -rf /dev/shm/DeepSeek-R1/* &&
    mkdir -p /dev/shm/DeepSeek-R1 &&
    echo '--- 正在复制模型到共享内存 (/dev/shm/DeepSeek-R1) 以获得最大速度 ---' &&
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress &&
    echo '--- 模型复制完成。启动SGLang服务 ---' &&
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --dtype auto
    --trust-remote-code
    --mem-fraction-static 0.92
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 16
    --kv-cache-dtype auto
  "
docker logs -f sglang_deepseek_r1_0528

# 列出所有tmux会话

tmux ls

# 返回到特定会话(例如名为"model_download"的会话)

tmux attach -t model_download

# 或者如果只有一个会话，直接使用

tmux attach

## 11. 性能优化指令

要进一步提升Token生成速度，可以在稳定运行的基础上，整合之前尝试过的所有优化项。以下是一个综合了性能和稳定性的推荐指令，您可以替换掉之前的启动命令。

### 优化点解析

1. **启用FP8 KV缓存 (`--kv-cache-dtype fp8_e5m2`)**: 这是最重要的优化之一。它将存储历史对话（KV缓存）的显存占用减半，显著降低显存带宽压力，从而提升速度。H200 GPU对FP8有原生硬件加速，能最大化此项优化的效果。
2. **预编译GEMM内核 (`sglang.compile_deep_gemm`)**: 在服务启动时，花费一些时间预先编译模型中最重要的计算内核。这是一次性的启动开销，但能换来后续推理过程的持续高速。
3. **增加连续解码步数 (`--num-continuous-decode-steps`)**: 将这个值从 `16`增加到 `20`。这让服务器在一次调度中能生成更多的token，从而提高长文本生成的吞吐量。
4. **优化GPU间通信 (`NCCL_P2P_LEVEL=NVL`)**: 添加这个环境变量可以指示NCCL库优先使用NVLink作为GPU之间的点对点通信方式，这是H200服务器上最快的通信链路。
5. **调整静态显存比例 (`--mem-fraction-static`)**: 鉴于之前发生了OOM（显存溢出）错误，我们将这个值设置在一个相对保守的 `0.90`。这能确保在启用上述所有优化的同时，仍有足够的空间进行计算，保证服务的稳定性。

### 优化后的启动指令

```bash
# 停止并清理已有容器
docker rm -f sglang_deepseek_r1

# 运行优化版服务
docker run -d \
  --gpus all \
  --ipc=host \
  --network host \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  -e NCCL_IB_DISABLE="0" \
  -e NCCL_DEBUG="INFO" \
  -e NCCL_P2P_LEVEL=NVL \
  -e NCCL_ALGO=RING \
  --shm-size=950g \
  -v /mnt/nvme2/DeepSeek-R1-0605:/model_files:ro \
  --name sglang_deepseek_r1 \
  lmsysorg/sglang:latest \
  /bin/bash -c " \
    set -e; \
    apt update && apt install -y rclone && \
    echo '--- 正在复制模型到共享内存 (/dev/shm/DeepSeek-R1) 以获得最大速度 ---' && \
    mkdir -p /dev/shm/DeepSeek-R1 && \
    rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress && \
    echo '--- 正在预编译DeepGEMM内核以加速推理 ---' && \
    python3 -m sglang.compile_deep_gemm --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code && \
    echo '--- 模型复制和编译完成。启动SGLang服务 ---' && \
    python3 -m sglang.launch_server \
      --model-path /dev/shm/DeepSeek-R1 \
      --tp 8 \
      --dtype auto \
      --trust-remote-code \
      --mem-fraction-static 0.90 \
      --served-model-name deepseek-r1 \
      --host 0.0.0.0 \
      --port 40000 \
      --attention-backend flashinfer \
      --enable-mixed-chunk \
      --num-continuous-decode-steps 20 \
      --kv-cache-dtype fp8_e5m2 \
  "
```

# 停止所有容器

docker rm -f $(docker ps -aq)

# 强制终止所有Python进程

pkill -9 -f python

# 强制终止所有sglang相关进程

pkill -9 -f sglang

# 停止容器

docker stop sglang_baichuan_ocr

# 删除容器

docker rm sglang_baichuan_ocr

# 查找并终止所有与百川模型相关的进程

pkill -f "baichuan-med-ocr"

# 或者更彻底地终止所有可能相关的进程

pkill -f "/dev/shm/BaichuanMed-OCR"

# 检查容器是否仍在运行

docker ps | grep baichuan

# 检查是否有相关进程仍在运行

ps aux | grep -i baichuan

# 停止并删除之前的容器

docker stop vllm_baichuan_ocr
docker rm vllm_baichuan_ocr

docker run -d 
  --gpus all 
  --network host 
  --ipc=host 
  --shm-size=64g 
  -e NCCL_DEBUG=INFO 
  -e NCCL_IB_DISABLE=1 
  -e NCCL_P2P_DISABLE=1 
  -e NCCL_SOCKET_IFNAME=lo 
  -e NCCL_SHM_DISABLE=1 
  -v /mnt/nvme2/BaichuanMed-OCR-7B:/model:ro 
  --name vllm_baichuan_ocr 
  vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 4 
  --max_num_seqs 4 
  --gpu-memory-utilization 0.22 
  --served-model-name BaichuanMed-OCR-7B 
  --max-model-len 64000 
  --port 5901 
  --max-num-batched-tokens 8192 
  --enable-chunked-prefill 
  --scheduler-delay-factor 0.1 
  --enable-prefix-caching 
  --dtype bfloat16 
  --additional-config '{"request_timeout":90}'

# 优化后的部署指令7_9_不会报错版

# 1. 先确保清理掉任何同名旧容器

docker rm -f vllm_baichuan_ocr 2>/dev/null || true

# 2. 运行为 BaichuanMed-OCR-7B 优化的双卡指令

docker run -d 
  --gpus all 
  -e CUDA_VISIBLE_DEVICES=0,1 
  --network host 
  --ipc=host 
  --ulimit memlock=-1 --ulimit stack=67108864 
  --shm-size 48g 
  -e NCCL_DEBUG=INFO 
  -e NCCL_P2P_LEVEL=NVL 
  -v /home/<USER>/BaichuanMed-OCR-7B:/model:ro 
  --name vllm_baichuan_ocr 
  docker.xuanyuan.me/vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 2 
  --gpu-memory-utilization 0.22 
  --max-model-len 8192 
  --dtype float16 
  --max-num-seqs 8 
  --max-num-batched-tokens 4096 
  --enable-chunked-prefill 
  --enable-prefix-caching 
  --served-model-name BaichuanMed-OCR-7B 
  --port 5901 
  --trust-remote-code

# 优化后的百川指令

docker run -d 
  --gpus all 
  --network host 
  --ipc=host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  --shm-size=128g 
  -e NCCL_IB_DISABLE=0 
  -e NCCL_DEBUG=INFO 
  -v /mnt/nvme2/BaichuanMed-OCR-7B:/model:ro 
  --name vllm_baichuan_ocr 
  vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 4 
  --max_num_seqs 2 
  --gpu-memory-utilization 0.22 
  --served-model-name BaichuanMed-OCR-7B 
  --max-model-len 64000 
  --port 5901 
  --max-num-batched-tokens 4096 
  --enable-chunked-prefill 
  --scheduler-delay-factor 0.1 
  --dtype bfloat16 
  --additional-config '{"request_timeout":60}'

docker run -d 
  --gpus all 
  --network host 
  --ipc=host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  --shm-size=128g 
  -e NCCL_IB_DISABLE=0 
  -e NCCL_DEBUG=INFO 
  -v /mnt/nvme2/BaichuanMed-OCR-7B:/model:ro 
  --name vllm_baichuan_ocr 
  vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 4 
  --max_num_seqs 1 
  --gpu-memory-utilization 0.22 
  --served-model-name BaichuanMed-OCR-7B 
  --max-model-len 64000 
  --port 5901 
  --max-num-batched-tokens 8192 
  --enable-chunked-prefill 
  --scheduler-delay-factor 0.1 
  --enable-prefix-caching 
  --dtype bfloat16 
  --additional-config '{"request_timeout":90}'

docker logs --tail 100 sglang_deepseek_r1

# 九章

# 停止并清理可能存在的旧容器

docker rm -f sglang_deepseek_r1

# 运行优化版服务

docker run -d --rm 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  --shm-size=950g 
  -v /data0/deepseek-ai/DeepSeek-R1:/model_files:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:v0.4.6.post3-cu124 
  /bin/bash -c '
    set -e;
    if ! command -v rclone &> /dev/null; then
      echo "--- 正在安装rclone... ---";
      apt-get update && apt-get install -y rclone;
    fi;
    echo "--- 检查共享内存中是否已存在模型... ---";
    if [ -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 模型已存在于/dev/shm，跳过复制。 ---";
    else
      echo "--- 正在复制模型到共享内存... ---";
      mkdir -p /dev/shm/DeepSeek-R1;
      rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress;
    fi;
    echo "--- 启动SGLang服务（使用稳定配置）... ---";
    python3 -m sglang.launch_server 
    --model-path /dev/shm/DeepSeek-R1
    --tensor-parallel-size 8
    --mem-fraction-static 0.9
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend triton
    --enable-mixed-chunk
    --num-continuous-decode-steps 16
    --kv-cache-dtype auto
    --trust-remote-code
  '

# 九章

# 停止当前容器

docker rm -f sglang_deepseek_r1

# 使用修正后的配置

docker run -d --rm 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v /data0/deepseek-ai/DeepSeek-R1:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:v0.4.6.post3-cu124 
  /bin/bash -c '
    set -e;
    if ! command -v rclone &> /dev/null; then
      echo "--- 正在安装rclone... ---";
      apt-get update && apt-get install -y rclone;
    fi;
    echo "--- 检查共享内存中是否已存在模型... ---";
    if [ -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 模型已存在于/dev/shm，跳过复制。 ---";
    else
      echo "--- 正在复制模型到共享内存... ---";
      mkdir -p /dev/shm/DeepSeek-R1;
      rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress;
    fi;
    echo "--- 预编译GEMM内核以加速推理... ---";
    python3 -m sglang.compile_deep_gemm --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code;
    echo "--- 启动SGLang服务（修正后的优化配置）... ---";
    python3 -m sglang.launch_server 
    --model-path /dev/shm/DeepSeek-R1
    --tensor-parallel-size 8
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 8
    --kv-cache-dtype auto
    --max-running-requests 128
    --chunked-prefill-size 4096
    --context-length 32768
    --trust-remote-code
  '
docker logs -f sglang_deepseek_r1
docker run -d --rm
  --gpus '"device=0,1,2,3,4,5,6,7"' 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 
  -e NCCL_IB_DISABLE=0 
  -e NCCL_DEBUG=INFO 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  -e NCCL_TREE_THRESHOLD=0 
  --shm-size=950g 
  -v /data0/deepseek-ai/DeepSeek-R1:/model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  --name sglang_deepseek_r1 
  lmsysorg/sglang:v0.4.6.post3-cu124 
  /bin/bash -c '
    set -e;

    echo "=== 环境检查 ===";
    echo "CUDA_VISIBLE_DEVICES: $CUDA_VISIBLE_DEVICES";
    nvidia-smi -L | head -8;

    if ! command -v rclone &> /dev/null; then
      echo "--- 正在安装rclone... ---";
      apt-get update && apt-get install -y rclone;
    fi;

    echo "--- 检查共享内存中是否已存在模型... ---";
    if [ -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 模型已存在于/dev/shm，跳过复制。 ---";
    else
      echo "--- 正在复制模型到共享内存... ---";
      mkdir -p /dev/shm/DeepSeek-R1;
      echo "开始时间: $(date)";
      rclone copy /model_files /dev/shm/DeepSeek-R1
    --transfers 96
    --checkers 32
    --progress
    --stats 30s;
      echo "复制完成时间: $(date)";
      echo "模型大小: $(du -sh /dev/shm/DeepSeek-R1)";
    fi;

    echo "--- 预编译GEMM内核以加速推理... ---";
    python3 -m sglang.compile_deep_gemm
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --trust-remote-code;

    echo "--- 启动SGLang服务（8x H200优化配置）... ---";
    echo "启动时间: $(date)";
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --tensor-parallel-size 8
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 8
    --kv-cache-dtype auto
    --max-running-requests 64
    --chunked-prefill-size 4096
    --context-length 32768
    --trust-remote-code
    --log-level info
  '

# 免费docker源

https://docker.xuanyuan.me/

docker run -d 
  --gpus all 
  --network host 
  --ipc=host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  --shm-size=128g 
  -e NCCL_IB_DISABLE=0 
  -e NCCL_DEBUG=INFO 
  -v /home/<USER>/BaichuanMed-OCR-7B:/model:ro 
  --name vllm_baichuan_ocr 
  docker.xuanyuan.me/vllm/vllm-openai:latest 
  --model /model 
  --tensor-parallel-size 4 
  --max_num_seqs 1 
  --gpu-memory-utilization 0.22 
  --served-model-name BaichuanMed-OCR-7B 
  --max-model-len 64000 
  --port 5901 
  --max-num-batched-tokens 8192 
  --enable-chunked-prefill 
  --scheduler-delay-factor 0.1 
  --enable-prefix-caching 
  --dtype bfloat16 
  --trust-remote-code 
  --additional-config '{"request_timeout":90}'

docker logs -f vllm_baichuan_ocr

# 草稿模型部署

docker run -d --rm 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE=0 
  -e NCCL_DEBUG=INFO 
  --shm-size=200g 
  -v /data0/deepseek-ai/DeepSeek-R1-0528:/draft_model:ro 
  --name vllm_deepseek_draft 
  vllm/vllm-openai:latest 
  --model /draft_model 
  --tensor-parallel-size 8 
  --max-model-len 16384 
  --dtype bfloat16 
  --port 40001 
  --gpu-memory-utilization 0.3 
  --served-model-name deepseek-r1-draft 
  --trust-remote-code 
  --max-num-seqs 8

huggingface-cli download 
    --resume-download
    --max-workers 8
    --local-dir /data0/deepseek-ai/deepseek-llm-7b-base
    deepseek-ai/deepseek-llm-7b-base

# 停止所有容器

docker rm -f $(docker ps -aq)

# 强制终止所有Python进程

pkill -9 -f python

# 强制终止所有sglang相关进程

pkill -9 -f sglang

# EAGLE 算法投机推理

###################  请先确认主模型在宿主机的真实路径  ###################

MAIN_MODEL_HOST_PATH="/data0/deepseek-ai/DeepSeek-R1"   # DeepSeek-R1-671B
docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  -v /home/<USER>/sglang_logs:/sgl_logs 
  --name sglang_deepseek_r1_eagle 
  91d767ae0063 
  /bin/bash -c '
    set -e

    # 安装 rclone（首次启动容器需执行一次）
    if ! command -v rclone &> /dev/null; then
      apt-get update && apt-get install -y rclone
    fi

    # 将主模型复制到高速共享内存 /dev/shm
    if [ ! -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 复制主模型到 /dev/shm/DeepSeek-R1 ---"
      mkdir -p /dev/shm/DeepSeek-R1
      rclone copy /main_model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress
    else
      echo "--- 主模型已在 /dev/shm，跳过复制 ---"
    fi

    # 预编译 DeepGEMM（FP8 MatrixMul 内核）
    python3 -m sglang.compile_deep_gemm
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --trust-remote-code

    # 启动 SGLang Server（EAGLE Multi-Token Prediction）
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --speculative-algorithm EAGLE
    --speculative-num-steps 3
    --speculative-eagle-topk 1
    --speculative-num-draft-tokens 32
    --tensor-parallel-size 8
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 8
    --kv-cache-dtype auto
    --max-running-requests 128
    --chunked-prefill-size 4096
    --context-length 65536
    --trust-remote-code
  '
docker logs -f sglang_deepseek_r1_eagle

# 激进版

MAIN_MODEL_HOST_PATH="/data0/deepseek-ai/DeepSeek-R1"

# DeepSeek MTP 使用内置草稿

docker run -d --name sglang_deepseek_r1_eagle_fast 
  --gpus all 
  --ipc=host --network host 
  --ulimit memlock=-1 --ulimit stack=67108864 
  -e NCCL_IB_DISABLE=0 -e NCCL_DEBUG=INFO 
  -e NCCL_P2P_LEVEL=NVL -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  -v /home/<USER>/sglang_logs:/sgl_logs 
  91d767ae0063 
/bin/bash -c "
set -e
if ! command -v rclone &> /dev/null; then  apt-get update && apt-get install -y rclone; fi
mkdir -p /dev/shm/DeepSeek-R1 && rclone copy /main_model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress
python3 -m sglang.compile_deep_gemm --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code
python3 -m sglang.launch_server 
  --model-path /dev/shm/DeepSeek-R1 
  --speculative-algorithm EAGLE 
  --speculative-num-draft-tokens 48 
  --speculative-eagle-topk 1 
  --speculative-num-steps 3 
  --kv-cache-dtype fp8_e5m2 
  --tensor-parallel-size 8 
  --mem-fraction-static 0.75 
  --num-continuous-decode-steps 12 
  --max-running-requests 64 
  --chunked-prefill-size 8192 
  --schedule-conservativeness 0.25 
  --served-model-name deepseek-r1 
  --attention-backend flashinfer 
  --enable-mixed-chunk 
  --host 0.0.0.0 --port 40000 
  --context-length 65536 
  --trust-remote-code
"

# 停止并清理可能存在的旧容器

docker rm -f sglang_deepseek_r1_standard

# 使用标准模式运行服务

docker run -d --rm 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v /data0/deepseek-ai/DeepSeek-R1:/model_files:ro 
  --name sglang_deepseek_r1_standard 
  docker.1ms.run/lmsysorg/sglang:latest 
  /bin/bash -c '
    set -e;
    if ! command -v rclone &> /dev/null; then
      echo "--- 正在安装rclone... ---";
      apt-get update && apt-get install -y rclone;
    fi;
    echo "--- 检查共享内存中是否已存在模型... ---";
    if [ -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 模型已存在于/dev/shm，跳过复制。 ---";
    else
      echo "--- 正在复制模型到共享内存... ---";
      mkdir -p /dev/shm/DeepSeek-R1;
      rclone copy /model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress;
    fi;
    echo "--- 预编译GEMM内核以加速推理... ---";
    python3 -m sglang.compile_deep_gemm --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code;
    echo "--- 启动SGLang服务（标准优化配置）... ---";
    python3 -m sglang.launch_server 
    --model-path /dev/shm/DeepSeek-R1
    --tensor-parallel-size 8
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 16
    --kv-cache-dtype auto
    --max-running-requests 128
    --context-length 8192
    --trust-remote-code
  '

# 查看实时日志

docker logs -f sglang_deepseek_r1_standard

# 记录expert

MAIN_MODEL_HOST_PATH="/data0/deepseek-ai/DeepSeek-R1"   # DeepSeek-R1-671B
docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  -v /home/<USER>/sglang_logs:/sgl_logs 
  -v /home/<USER>/deepseek_v2.py:/sgl-workspace/sglang/python/sglang/srt/models/deepseek_v2.py:ro 
  --name sglang_deepseek_r1_eagle 
  91d767ae0063 
  /bin/bash -c '
    set -e

    # 安装 rclone（首次启动容器需执行一次）
    if ! command -v rclone &> /dev/null; then
      apt-get update && apt-get install -y rclone
    fi

    # 将主模型复制到高速共享内存 /dev/shm
    if [ ! -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 复制主模型到 /dev/shm/DeepSeek-R1 ---"
      mkdir -p /dev/shm/DeepSeek-R1
      rclone copy /main_model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress
    else
      echo "--- 主模型已在 /dev/shm，跳过复制 ---"
    fi

    # 预编译 DeepGEMM（FP8 MatrixMul 内核）
    python3 -m sglang.compile_deep_gemm
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --trust-remote-code

    # 启动 SGLang Server（EAGLE Multi-Token Prediction）
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1
    --disable-cuda-graph
    --speculative-algorithm EAGLE
    --speculative-num-steps 1
    --speculative-eagle-topk 1
    --speculative-num-draft-tokens 4
    --tensor-parallel-size 8
    --mem-fraction-static 0.7
    --served-model-name deepseek-r1
    --enable-deepep-moe
    --host 0.0.0.0
    --port 40000
    --attention-backend flashinfer
    --enable-mixed-chunk
    --num-continuous-decode-steps 4
    --kv-cache-dtype auto
    --max-running-requests 128
    --chunked-prefill-size 2048
    --context-length 32768
    --trust-remote-code
  '
docker logs -f sglang_deepseek_r1_eagle

# Eagle3部署

############################# 变量区 #############################
MAIN_MODEL_HOST_PATH="/data0/deepseek-ai/DeepSeek-R1"          # 671B 主模型
DRAFT_MODEL_HOST_PATH="/data0/deepseek-ai/eagle3_draft_8b"     # 草稿模型（请确认实际路径）

########################### docker run ##########################
docker run -d --name sglang_deepseek_r1_eagle3 
  --gpus all 
  --ipc=host --network host 
  --ulimit memlock=-1 --ulimit stack=67108864 
  -e NCCL_IB_DISABLE=0 -e NCCL_DEBUG=INFO 
  -e NCCL_P2P_LEVEL=NVL -e NCCL_ALGO=RING 
  --shm-size=950g 
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro 
  -v ${DRAFT_MODEL_HOST_PATH}:/draft_model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  -v /home/<USER>/sglang_logs:/sgl_logs 
  91d767ae0063 
/bin/bash -c "
set -e

# 安装 rclone（首次启动容器需执行一次）

if ! command -v rclone &> /dev/null; then
  apt-get update && apt-get install -y rclone
fi

# ---------- 复制主模型 ----------

if [ ! -f /dev/shm/DeepSeek-R1/config.json ]; then
  echo 'Copy main model to /dev/shm/DeepSeek-R1'
  mkdir -p /dev/shm/DeepSeek-R1
  rclone copy /main_model_files /dev/shm/DeepSeek-R1 --transfers 96 --progress
fi

# ---------- 复制草稿模型 ----------

if [ ! -f /dev/shm/eagle3_draft_8b/config.json ]; then
  echo 'Copy draft model to /dev/shm/eagle3_draft_8b'
  mkdir -p /dev/shm/eagle3_draft_8b
  rclone copy /draft_model_files /dev/shm/eagle3_draft_8b --transfers 32 --progress
fi

# ---------- 预编译 GEMM ----------

python3 -m sglang.compile_deep_gemm 
  --model-path /dev/shm/DeepSeek-R1 --tp 8 --trust-remote-code

# ---------- 启动 SGLang ----------

python3 -m sglang.launch_server 
  --model-path /dev/shm/DeepSeek-R1 
  --speculative-algorithm EAGLE3 
  --speculative-draft-model-path /dev/shm/eagle3_draft_8b 
  --speculative-num-draft-tokens 60 
  --speculative-eagle-topk 2 
  --tensor-parallel-size 8 
  --mem-fraction-static 0.7 
  --served-model-name deepseek-r1 
  --host 0.0.0.0 --port 40000 
  --attention-backend flashinfer 
  --enable-mixed-chunk 
  --num-continuous-decode-steps 8 
  --kv-cache-dtype auto 
  --max-running-requests 128 
  --chunked-prefill-size 4096 
  --context-length 65536 
  --trust-remote-code
"
docker logs -f sglang_deepseek_r1_eagle3

#!/bin/bash

# SGLang H200 优化配置 - DeepSeek-R1 EAGLE推测解码

# 针对8×H200硬件的最优参数配置

MAIN_MODEL_HOST_PATH="/data0/deepseek-ai/DeepSeek-R1"   # DeepSeek-R1-671B

docker run -d 
  --gpus all 
  --ipc=host 
  --network host 
  --ulimit memlock=-1 
  --ulimit stack=67108864 
  -e NCCL_IB_DISABLE="0" 
  -e NCCL_DEBUG="INFO" 
  -e NCCL_P2P_LEVEL=NVL 
  -e NCCL_ALGO=RING 
  -e CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 
  -e NCCL_TOPO_FILE=/opt/microsoft/ndv5-topo.xml 
  --shm-size=950g 
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro 
  -v $(which rclone):/usr/bin/rclone:ro 
  -v /home/<USER>/sglang_logs:/sgl_logs 
  --name sglang_deepseek_r1_eagle_optimized 
  91d767ae0063 
  /bin/bash -c '
    set -e

    # 安装 rclone（首次启动容器需执行一次）
    if ! command -v rclone &> /dev/null; then
      apt-get update && apt-get install -y rclone
    fi

    # 将主模型复制到高速共享内存 /dev/shm
    if [ ! -f /dev/shm/DeepSeek-R1/config.json ]; then
      echo "--- 复制主模型到 /dev/shm/DeepSeek-R1 ---"
      mkdir -p /dev/shm/DeepSeek-R1
      rclone copy /main_model_files /dev/shm/DeepSeek-R1 --transfers 128 --progress
    else
      echo "--- 主模型已在 /dev/shm，跳过复制 ---"
    fi

    # 预编译 DeepGEMM（FP8 MatrixMul 内核）
    echo "--- 预编译 DeepGEMM FP8 内核 ---"
    python3 -m sglang.compile_deep_gemm
    --model-path /dev/shm/DeepSeek-R1
    --tp 8
    --trust-remote-code

    # 预热CUDA内核
    echo "--- 预热CUDA内核 ---"
    python3 -c "
import torch
torch.cuda.init()
for i in range(8):
    torch.cuda.set_device(i)
    torch.cuda.empty_cache()
    print(f'GPU {i} initialized')
"

    # 启动 SGLang Server（H200优化版）
    echo "--- 启动 SGLang Server (H200优化版) ---"
    python3 -m sglang.launch_server
    --model-path /dev/shm/DeepSeek-R1

    `# === EAGLE推测解码优化 ===` 
    --speculative-algorithm EAGLE
    --speculative-num-steps 4
    --speculative-eagle-topk 4
    --speculative-num-draft-tokens 24
    --speculative-draft-tensor-parallel-size 4
    --speculative-disable-by-batch-size 64

    `# === 并行和内存优化 ===` 
    --tensor-parallel-size 8
    --mem-fraction-static 0.78
    --kv-cache-dtype fp8
    --enable-prefix-caching
    --radix-cache-capacity 0.9
    --disable-disk-cache
    --enable-overlap-schedule

    `# === 注意力后端优化 ===` 
    --attention-backend flashinfer
    --enable-flashinfer-mla
    --flashinfer-workspace-size 4096
    --enable-cuda-graph
    --cuda-graph-max-bs 128

    `# === 批处理和调度优化 ===` 
    --max-running-requests 512
    --chunked-prefill-size 16384
    --num-continuous-decode-steps 32
    --max-prefill-tokens 32768
    --schedule-policy lpm
    --enable-mixed-chunk
    --max-total-tokens 2097152

    `# === H200特定优化 ===` 
    --enable-torch-compile
    --torch-compile-max-bs 128
    --quantization fp8
    --load-format auto
    --enable-dp-attention
    --dp-size 1

    `# === 性能调优 ===` 
    --disable-log-requests
    --log-level WARNING
    --enable-metrics
    --metrics-port 8001

    `# === 基础配置 ===` 
    --served-model-name deepseek-r1
    --host 0.0.0.0
    --port 40000
    --context-length 131072
    --trust-remote-code
    --worker-use-ray
  '

echo "--- 启动完成，查看日志 ---"
docker logs -f sglang_deepseek_r1_eagle_optimized


### 部署8B模型


```python
MAIN_MODEL_HOST_PATH="/data1/Qwen3-8B"  # 模型所在目录
PORT=40010                               # 避免与40000冲突

docker run -d \
  --gpus "device=7" \
  --ipc=host \
  --network host \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  -e NCCL_IB_DISABLE="0" \
  -e NCCL_DEBUG="INFO" \
  -e NCCL_P2P_LEVEL=NVL \
  -e NCCL_ALGO=RING \
  --shm-size=64g \
  -v ${MAIN_MODEL_HOST_PATH}:/main_model_files:ro \
  -v $(which rclone):/usr/bin/rclone:ro \
  -v /home/<USER>/sglang_logs:/sgl_logs \
  --name sglang_qwen3_8b \
  91d767ae0063 \
  /bin/bash -lc '
    set -euo pipefail

    if ! command -v rclone &> /dev/null; then
      apt-get update && apt-get install -y rclone
    fi

    echo "--- 清理并重新复制到 /dev/shm/Qwen3-8B ---"
    rm -rf /dev/shm/Qwen3-8B
    mkdir -p /dev/shm/Qwen3-8B
    rclone copy /main_model_files /dev/shm/Qwen3-8B --transfers 64 --checkers 32 --progress
    sync

    echo "--- 快速校验 safetensors 分片 ---"
    python3 - <<PY
from safetensors.torch import load_file
import glob, sys
bad=[]
for f in sorted(glob.glob("/dev/shm/Qwen3-8B/model-*.safetensors")):
    try:
        load_file(f, device="cpu")
        print("OK", f, flush=True)
    except Exception as e:
        print("BAD", f, e, flush=True)
        bad.append(f)
if bad:
    print("Found BAD shards:", bad)
    sys.exit(1)
print("ALL_OK")
PY

    echo "--- 预编译 DeepGEMM（TP=1）---"
    python3 -m sglang.compile_deep_gemm \
      --model-path /dev/shm/Qwen3-8B \
      --tp 1 \
      --trust-remote-code

    echo "--- 启动 SGLang ---"
    python3 -m sglang.launch_server \
      --model-path /dev/shm/Qwen3-8B \
      --tensor-parallel-size 1 \
      --mem-fraction-static 0.6 \
      --served-model-name qwen3-8b \
      --host 0.0.0.0 \
      --port '"$PORT"' \
      --attention-backend flashinfer \
      --enable-mixed-chunk \
      --num-continuous-decode-steps 8 \
      --kv-cache-dtype auto \
      --max-running-requests 64 \
      --chunked-prefill-size 4096 \
      --context-length 8192 \
      --trust-remote-code
  '
docker logs -f sglang_qwen3_8b

```
